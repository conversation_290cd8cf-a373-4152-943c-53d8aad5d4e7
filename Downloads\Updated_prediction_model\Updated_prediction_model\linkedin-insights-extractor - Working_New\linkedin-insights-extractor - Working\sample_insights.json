{"company_profile": {"employees_current": 15000, "hires_last_12_months": 2500, "growth_rate_percent_1y": 18, "attrition_percent_1y": 12, "open_jobs": 450, "industry": "Technology", "headquarters": "Seattle, WA", "founded_year": 1975, "company_type": "Public Company"}, "top_functions_percent": [{"function": "Engineering", "percent_employees": 35}, {"function": "Sales", "percent_employees": 20}, {"function": "Marketing", "percent_employees": 15}, {"function": "Operations", "percent_employees": 12}, {"function": "Human Resources", "percent_employees": 8}], "function_headcount": {"Engineering": 5250, "Sales": 3000, "Marketing": 2250, "Operations": 1800, "Human Resources": 1200}, "skills": {"top_skills": [{"skill": "Python", "professionals": 1200}, {"skill": "Machine Learning", "professionals": 800}, {"skill": "Artificial Intelligence", "professionals": 650}, {"skill": "Deep Learning", "professionals": 500}, {"skill": "TensorFlow", "professionals": 450}, {"skill": "PyTorch", "professionals": 400}, {"skill": "Data Science", "professionals": 750}, {"skill": "Natural Language Processing", "professionals": 300}, {"skill": "Computer Vision", "professionals": 280}, {"skill": "MLOps", "professionals": 220}], "fastest_growing": [{"skill": "Generative AI", "growth_percent": 150}, {"skill": "Large Language Models", "growth_percent": 120}, {"skill": "MLOps", "growth_percent": 85}, {"skill": "Transformer Models", "growth_percent": 75}, {"skill": "Prompt Engineering", "growth_percent": 200}]}, "attrition_breakdown": {"by_function": [{"function": "Engineering", "attrition_percent": 15}, {"function": "Sales", "attrition_percent": 18}, {"function": "Marketing", "attrition_percent": 10}, {"function": "Operations", "attrition_percent": 8}, {"function": "Human Resources", "attrition_percent": 12}], "by_location": [{"location": "Seattle, WA", "attrition_percent": 12}, {"location": "San Francisco, CA", "attrition_percent": 16}, {"location": "Austin, TX", "attrition_percent": 10}, {"location": "Boston, MA", "attrition_percent": 14}, {"location": "Remote", "attrition_percent": 11}]}, "hiring_trends": {"seasonal_patterns": [{"quarter": "Q1", "hiring_multiplier": 1.2}, {"quarter": "Q2", "hiring_multiplier": 1.0}, {"quarter": "Q3", "hiring_multiplier": 0.8}, {"quarter": "Q4", "hiring_multiplier": 1.1}], "market_demand": {"ai_engineers": {"demand_growth": 45, "supply_shortage": 35, "avg_time_to_fill": 120}}}, "competitive_landscape": {"talent_competition": "High", "salary_inflation": 25, "remote_work_adoption": 85}}