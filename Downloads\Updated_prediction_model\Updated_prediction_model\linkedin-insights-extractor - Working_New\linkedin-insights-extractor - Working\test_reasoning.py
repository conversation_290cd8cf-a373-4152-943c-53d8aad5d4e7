#!/usr/bin/env python3
"""
Test script to examine current reasoning output and improve it.
"""

import json
from linkedin_insights.linkedin_insights_llm_predictor import predict_headcount_llm
from linkedin_insights.timeline_allocator import allocate_buckets
from linkedin_insights.timeline_reasoning_llm import add_reasoning

def test_current_reasoning():
    """Test the current reasoning implementation."""
    
    # Load sample insights
    with open("sample_insights.json", "r") as f:
        insights = json.load(f)
    
    role = "AI Engineer"
    
    print("=== TESTING CURRENT REASONING IMPLEMENTATION ===\n")
    
    # Step 1: Get headcount prediction
    print("1. Getting headcount prediction...")
    full_result = predict_headcount_llm(insights, role=role, verbose=True, return_full=True)
    headcount = full_result["result"]["headcount"]
    confidence = full_result["result"]["forecast_confidence"]
    
    print(f"   Predicted headcount: {headcount}")
    print(f"   Confidence: {confidence}")
    print(f"   Calculation details: {json.dumps(full_result['calc'], indent=2)}")
    
    # Step 2: Allocate to timeline
    print("\n2. Allocating to timeline buckets...")
    timeline = allocate_buckets(headcount, role, insights)
    
    for bucket in timeline:
        print(f"   {bucket['timeframe']}: {bucket['count']} hires")
    
    # Step 3: Add current reasoning
    print("\n3. Adding current reasoning...")
    timeline_with_reasoning = add_reasoning(timeline, role, insights)
    
    print("\n=== CURRENT REASONING OUTPUT ===")
    for bucket in timeline_with_reasoning:
        print(f"\n{bucket['timeframe']}: {bucket['count']} hires")
        print(f"Reasoning: {bucket['reasoning']}")
    
    return timeline_with_reasoning, full_result

if __name__ == "__main__":
    test_current_reasoning()
