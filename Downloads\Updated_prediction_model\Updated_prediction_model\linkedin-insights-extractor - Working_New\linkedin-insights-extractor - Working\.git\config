[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[user]
	name = Kevin1899
	email = kevin<PERSON>n<PERSON><PERSON><EMAIL>
[remote "origin"]
	url = https://github.com/Kevin1899/Headcount-Generation-and-Timeline-Distibution-from-Linkedin-Insights.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "master"]
	remote = origin
	merge = refs/heads/master
