# timeline_reasoning_llm.py

from linkedin_insights.llm_utils import call_gpt
from linkedin_insights.linkedin_insights_llm_predictor import predict_headcount_llm
import math


def add_reasoning(timeline, role, insights):
    """
    Given a list of {timeframe, count}, returns the same list with mathematically-proven reasoning added per bucket.
    Uses enhanced mathematical analysis to justify each hiring count.
    """
    # Get the full prediction details for mathematical breakdown
    full_prediction = predict_headcount_llm(insights, role=role, return_full=True)

    # Calculate mathematical justifications
    mathematical_breakdown = calculate_mathematical_breakdown(timeline, full_prediction, insights)

    # Build enhanced prompt with mathematical context
    prompt = build_enhanced_prompt(timeline, role, mathematical_breakdown, full_prediction)
    response = call_gpt(prompt, temperature=0.2)  # Lower temperature for more precise reasoning

    # Parse bullet point responses
    justifications = parse_justifications(response, len(timeline))

    # Attach reasoning per bucket
    updated = []
    for i, bucket in enumerate(timeline):
        updated.append({
            "timeframe": bucket["timeframe"],
            "count": bucket["count"],
            "reasoning": justifications[i],
            "mathematical_breakdown": mathematical_breakdown[i] if i < len(mathematical_breakdown) else None,
        })

    return updated


def calculate_mathematical_breakdown(timeline, full_prediction, insights):
    """
    Calculate detailed mathematical breakdown for each timeframe.
    Returns list of mathematical justifications per bucket.
    """
    calc_details = full_prediction.get("calc", {})
    result_details = full_prediction.get("result", {})

    # Extract key metrics
    total_headcount = result_details.get("headcount", 0)
    active_demand = result_details.get("active_demand", 0)
    growth_demand = result_details.get("growth_demand", 0)
    backfill_demand = result_details.get("backfill_demand", 0)

    # Get timeline weights for proportional allocation
    bucket_weights = [1, 2, 3, 3, 3]  # From config.py BUCKETS
    total_weight = sum(bucket_weights)

    # Calculate BUSINESS-LOGICAL breakdown per timeframe
    # BACKFILL (attrition replacement) = URGENT (0-3 months priority)
    # ACTIVE (immediate roles) = CRITICAL (0-1 month priority)
    # GROWTH (expansion) = PLANNED (distributed across 3-12 months)

    breakdown = []

    # First, get ideal distributions based on business logic
    ideal_backfill = distribute_backfill_demand(backfill_demand)
    ideal_active = distribute_active_demand(active_demand)
    ideal_growth = distribute_growth_demand(growth_demand)

    # Adjust distributions to match actual timeline allocation while preserving business logic
    actual_active_dist = [0] * len(timeline)
    actual_backfill_dist = [0] * len(timeline)
    actual_growth_dist = [0] * len(timeline)

    # First pass: distribute based on business logic proportions
    for i, bucket in enumerate(timeline):
        actual_count = bucket["count"]
        ideal_active_i = ideal_active[i] if i < len(ideal_active) else 0
        ideal_backfill_i = ideal_backfill[i] if i < len(ideal_backfill) else 0
        ideal_growth_i = ideal_growth[i] if i < len(ideal_growth) else 0
        ideal_total = ideal_active_i + ideal_backfill_i + ideal_growth_i

        if ideal_total > 0:
            # Proportional distribution
            actual_active_dist[i] = round((ideal_active_i / ideal_total) * actual_count)
            actual_backfill_dist[i] = round((ideal_backfill_i / ideal_total) * actual_count)
            actual_growth_dist[i] = round((ideal_growth_i / ideal_total) * actual_count)

            # Adjust for rounding errors
            current_total = actual_active_dist[i] + actual_backfill_dist[i] + actual_growth_dist[i]
            diff = actual_count - current_total

            if diff != 0:
                # Add/subtract from the largest component
                if ideal_backfill_i >= ideal_active_i and ideal_backfill_i >= ideal_growth_i:
                    actual_backfill_dist[i] += diff
                elif ideal_active_i >= ideal_growth_i:
                    actual_active_dist[i] += diff
                else:
                    actual_growth_dist[i] += diff
        else:
            # Fallback distribution based on timeframe
            if i <= 1:  # 0-3 months: prioritize backfill and active
                actual_backfill_dist[i] = math.ceil(actual_count * 0.6)
                actual_active_dist[i] = math.ceil(actual_count * 0.3)
                actual_growth_dist[i] = actual_count - actual_backfill_dist[i] - actual_active_dist[i]
            else:  # 3+ months: prioritize growth
                actual_growth_dist[i] = math.ceil(actual_count * 0.7)
                actual_backfill_dist[i] = math.ceil(actual_count * 0.2)
                actual_active_dist[i] = actual_count - actual_growth_dist[i] - actual_backfill_dist[i]

        # Ensure no negative values
        actual_active_dist[i] = max(0, actual_active_dist[i])
        actual_backfill_dist[i] = max(0, actual_backfill_dist[i])
        actual_growth_dist[i] = max(0, actual_growth_dist[i])

    # Second pass: adjust totals to match original predictions exactly
    total_active_distributed = sum(actual_active_dist)
    total_backfill_distributed = sum(actual_backfill_dist)
    total_growth_distributed = sum(actual_growth_dist)

    # Adjust active demand
    active_diff = active_demand - total_active_distributed
    if active_diff != 0:
        # Find the timeframe with highest active allocation to adjust
        max_active_idx = actual_active_dist.index(max(actual_active_dist)) if max(actual_active_dist) > 0 else 0
        actual_active_dist[max_active_idx] += active_diff
        actual_active_dist[max_active_idx] = max(0, actual_active_dist[max_active_idx])

    # Adjust backfill demand
    backfill_diff = backfill_demand - total_backfill_distributed
    if backfill_diff != 0:
        # Find the timeframe with highest backfill allocation to adjust
        max_backfill_idx = actual_backfill_dist.index(max(actual_backfill_dist)) if max(actual_backfill_dist) > 0 else 0
        actual_backfill_dist[max_backfill_idx] += backfill_diff
        actual_backfill_dist[max_backfill_idx] = max(0, actual_backfill_dist[max_backfill_idx])

    # Adjust growth demand
    growth_diff = growth_demand - total_growth_distributed
    if growth_diff != 0:
        # Find the timeframe with highest growth allocation to adjust
        max_growth_idx = actual_growth_dist.index(max(actual_growth_dist)) if max(actual_growth_dist) > 0 else 2
        actual_growth_dist[max_growth_idx] += growth_diff
        actual_growth_dist[max_growth_idx] = max(0, actual_growth_dist[max_growth_idx])

    # Now create breakdown for each timeframe
    for i, bucket in enumerate(timeline):
        timeframe_active = actual_active_dist[i]
        timeframe_backfill = actual_backfill_dist[i]
        timeframe_growth = actual_growth_dist[i]

        # Final verification and adjustment to ensure exact match
        current_total = timeframe_active + timeframe_backfill + timeframe_growth
        if current_total != bucket["count"]:
            diff = bucket["count"] - current_total
            # Add difference to the largest component
            if timeframe_backfill >= timeframe_active and timeframe_backfill >= timeframe_growth:
                timeframe_backfill += diff
            elif timeframe_active >= timeframe_growth:
                timeframe_active += diff
            else:
                timeframe_growth += diff

            # Ensure no negative values
            timeframe_active = max(0, timeframe_active)
            timeframe_backfill = max(0, timeframe_backfill)
            timeframe_growth = max(0, timeframe_growth)

        # Get market timing factors
        market_factors = get_market_timing_factors(i, insights)

        # Calculate business impact metrics
        business_metrics = calculate_business_impact(bucket["count"], timeframe_active, timeframe_growth, timeframe_backfill, insights)

        # Calculate business logic priority
        priority_logic = determine_priority_logic(i, timeframe_active, timeframe_growth, timeframe_backfill)

        breakdown.append({
            "timeframe": bucket["timeframe"],
            "count": bucket["count"],
            "active_demand_component": timeframe_active,
            "growth_demand_component": timeframe_growth,
            "backfill_demand_component": timeframe_backfill,
            "market_factors": market_factors,
            "business_impact": business_metrics,
            "priority_logic": priority_logic,
            "mathematical_total": timeframe_active + timeframe_growth + timeframe_backfill,
            "verification": timeframe_active + timeframe_growth + timeframe_backfill == bucket["count"],
            "business_justification": get_business_justification(i, timeframe_active, timeframe_growth, timeframe_backfill)
        })

    return breakdown


def distribute_backfill_demand(total_backfill):
    """
    Distribute backfill demand with URGENT priority (0-3 months).
    Backfill = replacing people who left due to attrition - CANNOT WAIT!
    """
    if total_backfill == 0:
        return [0, 0, 0, 0, 0]

    # 70% in first 3 months (0-1: 30%, 1-3: 40%), 30% in remaining months
    distribution = [
        math.ceil(total_backfill * 0.30),  # 0-1 month: 30% (IMMEDIATE)
        math.ceil(total_backfill * 0.40),  # 1-3 months: 40% (URGENT)
        math.ceil(total_backfill * 0.15),  # 3-6 months: 15% (PLANNED)
        math.ceil(total_backfill * 0.10),  # 6-9 months: 10% (PLANNED)
        math.ceil(total_backfill * 0.05),  # 9+ months: 5% (PLANNED)
    ]

    # Adjust to match total exactly
    current_sum = sum(distribution)
    diff = total_backfill - current_sum
    if diff != 0:
        # Add/subtract from the largest bucket (1-3 months)
        distribution[1] += diff

    return distribution


def distribute_active_demand(total_active):
    """
    Distribute active demand with CRITICAL priority (0-1 months).
    Active = immediate open roles that need to be filled NOW.
    """
    if total_active == 0:
        return [0, 0, 0, 0, 0]

    # 80% in first month, 20% in second timeframe
    distribution = [
        math.ceil(total_active * 0.80),  # 0-1 month: 80% (CRITICAL)
        math.ceil(total_active * 0.20),  # 1-3 months: 20% (HIGH)
        0,  # 3-6 months: 0% (active roles can't wait this long)
        0,  # 6-9 months: 0%
        0,  # 9+ months: 0%
    ]

    # Adjust to match total exactly
    current_sum = sum(distribution)
    diff = total_active - current_sum
    if diff != 0:
        distribution[0] += diff  # Add to first month

    return distribution


def distribute_growth_demand(total_growth):
    """
    Distribute growth demand with PLANNED priority (3-12 months).
    Growth = expansion roles for future business needs.
    """
    if total_growth == 0:
        return [0, 0, 0, 0, 0]

    # Minimal in first 3 months, majority in months 3-12
    distribution = [
        math.ceil(total_growth * 0.05),  # 0-1 month: 5% (minimal)
        math.ceil(total_growth * 0.10),  # 1-3 months: 10% (some planning)
        math.ceil(total_growth * 0.30),  # 3-6 months: 30% (MAIN GROWTH)
        math.ceil(total_growth * 0.30),  # 6-9 months: 30% (MAIN GROWTH)
        math.ceil(total_growth * 0.25),  # 9+ months: 25% (STRATEGIC)
    ]

    # Adjust to match total exactly
    current_sum = sum(distribution)
    diff = total_growth - current_sum
    if diff != 0:
        # Add/subtract from the largest bucket (3-6 months)
        distribution[2] += diff

    return distribution


def determine_priority_logic(timeframe_index, active, growth, backfill):
    """
    Determine the business priority logic for each timeframe.
    """
    timeframes = ["0–1 month", "1–3 months", "3–6 months", "6–9 months", "9+ months"]
    timeframe_name = timeframes[timeframe_index] if timeframe_index < len(timeframes) else "unknown"

    if timeframe_index == 0:  # 0-1 month
        return {
            "primary_focus": "CRITICAL: Active roles + Immediate backfill",
            "urgency": "MAXIMUM",
            "rationale": f"Active roles ({active}) cannot wait - immediate business impact. Backfill ({backfill}) prevents operational gaps.",
            "delay_risk": "HIGH - Direct revenue impact and team overload"
        }
    elif timeframe_index == 1:  # 1-3 months
        return {
            "primary_focus": "URGENT: Remaining backfill + Some growth",
            "urgency": "HIGH",
            "rationale": f"Complete backfill hiring ({backfill}) to stabilize teams. Begin growth hiring ({growth}) for Q2 needs.",
            "delay_risk": "MEDIUM-HIGH - Attrition compounds, growth delayed"
        }
    else:  # 3+ months
        return {
            "primary_focus": "PLANNED: Growth-focused expansion",
            "urgency": "MODERATE",
            "rationale": f"Strategic growth hiring ({growth}) for future capacity. Minimal backfill ({backfill}) as teams stabilized.",
            "delay_risk": "MEDIUM - Strategic positioning and market opportunities"
        }


def get_business_justification(timeframe_index, active, growth, backfill):
    """
    Generate specific business justification based on demand components.
    """
    total = active + growth + backfill

    if timeframe_index == 0:  # 0-1 month
        return f"CRITICAL HIRING: {active} immediate roles + {backfill} attrition replacements = {total} hires needed to prevent operational disruption"
    elif timeframe_index == 1:  # 1-3 months
        return f"URGENT STABILIZATION: {backfill} remaining backfills + {growth} early growth roles = {total} hires to complete team stabilization"
    else:  # 3+ months
        return f"STRATEGIC GROWTH: {growth} expansion roles + {backfill} projected backfills = {total} hires for planned business growth"


def get_market_timing_factors(timeframe_index, insights):
    """
    Extract market timing factors that justify hiring in specific timeframes.
    """
    factors = {
        "seasonal_multiplier": 1.0,
        "attrition_risk": "standard",
        "demand_urgency": "normal",
        "budget_cycle": "regular"
    }

    # Extract seasonal patterns if available
    hiring_trends = insights.get("hiring_trends", {})
    seasonal_patterns = hiring_trends.get("seasonal_patterns", [])

    if seasonal_patterns:
        # Map timeframe index to quarters (rough approximation)
        quarter_map = {0: "Q1", 1: "Q1", 2: "Q2", 3: "Q3", 4: "Q4"}
        quarter = quarter_map.get(timeframe_index, "Q1")

        for pattern in seasonal_patterns:
            if pattern.get("quarter") == quarter:
                factors["seasonal_multiplier"] = pattern.get("hiring_multiplier", 1.0)
                break

    # Determine urgency based on timeframe
    if timeframe_index == 0:  # 0-1 month
        factors["demand_urgency"] = "critical"
        factors["attrition_risk"] = "immediate"
    elif timeframe_index == 1:  # 1-3 months
        factors["demand_urgency"] = "high"
        factors["attrition_risk"] = "near-term"
    else:
        factors["demand_urgency"] = "planned"
        factors["attrition_risk"] = "projected"

    return factors


def calculate_business_impact(count, active, growth, backfill, insights):
    """
    Calculate business impact metrics for hiring decisions.
    """
    # Extract market intelligence
    competitive_landscape = insights.get("competitive_landscape", {})
    hiring_trends = insights.get("hiring_trends", {})

    # Calculate cost of delay (assuming average time to fill is 120 days)
    avg_time_to_fill = hiring_trends.get("market_demand", {}).get("ai_engineers", {}).get("avg_time_to_fill", 120)
    cost_of_delay_days = avg_time_to_fill * 0.8  # 80% of time to fill as delay cost

    # Calculate competitive risk
    talent_competition = competitive_landscape.get("talent_competition", "Medium")
    competition_multiplier = {"High": 1.5, "Medium": 1.2, "Low": 1.0}.get(talent_competition, 1.2)

    # Revenue impact estimation (rough calculation)
    # Assume each AI engineer contributes ~$500K annually in value
    annual_value_per_engineer = 500000
    monthly_value_per_engineer = annual_value_per_engineer / 12

    return {
        "revenue_at_risk": count * monthly_value_per_engineer,
        "cost_of_delay_days": cost_of_delay_days,
        "competition_risk_multiplier": competition_multiplier,
        "active_roles_urgency": "critical" if active > 0 else "none",
        "growth_impact": "high" if growth > count * 0.4 else "moderate",
        "attrition_risk": "immediate" if backfill > count * 0.3 else "manageable"
    }


def build_enhanced_prompt(timeline, role, mathematical_breakdown, full_prediction):
    """
    Builds an enhanced prompt with mathematical breakdown and business justification.
    """
    # Build timeline description with mathematical breakdown
    timeline_description = []
    for i, bucket in enumerate(timeline):
        breakdown = mathematical_breakdown[i] if i < len(mathematical_breakdown) else {}

        desc = f"- {bucket['timeframe']}: {bucket['count']} hire(s)"
        if breakdown:
            active = breakdown.get('active_demand_component', 0)
            backfill = breakdown.get('backfill_demand_component', 0)
            growth = breakdown.get('growth_demand_component', 0)

            desc += f"\n  📊 MATHEMATICAL BREAKDOWN:"
            desc += f"\n    • Backfill (urgent): {backfill} hires"
            desc += f"\n    • Active (critical): {active} hires"
            desc += f"\n    • Growth (planned): {growth} hires"
            total_calc = active + backfill + growth
            desc += f"\n    • TOTAL: {backfill} + {active} + {growth} = {total_calc} hires"
            desc += f"\n    • Verification: {total_calc == bucket['count']} ({'✅ CORRECT' if total_calc == bucket['count'] else '❌ ERROR'})"

            market_factors = breakdown.get('market_factors', {})
            desc += f"\n  🎯 BUSINESS PRIORITY: {market_factors.get('demand_urgency', 'normal').upper()}"

            # Add business justification
            business_just = breakdown.get('business_justification', '')
            if business_just:
                desc += f"\n  💡 LOGIC: {business_just}"

        timeline_description.append(desc)

    timeline_text = "\n\n".join(timeline_description)

    # Extract key prediction metrics
    result = full_prediction.get("result", {})
    total_active = result.get("active_demand", 0)
    total_growth = result.get("growth_demand", 0)
    total_backfill = result.get("backfill_demand", 0)
    total_headcount = result.get("headcount", 0)
    confidence = result.get("forecast_confidence", 0.5)

    return f"""
You are an expert in strategic workforce planning and mathematical modeling.

A company is hiring for the role of **{role}**. Here is the BUSINESS-LOGICAL hiring breakdown:

TOTAL PREDICTION: {total_headcount} hires over 12 months (Confidence: {confidence:.2f})
- Active demand (immediate open roles): {total_active} → CRITICAL (0-1 months)
- Backfill demand (attrition replacement): {total_backfill} → URGENT (0-3 months)
- Growth demand (expansion needs): {total_growth} → PLANNED (3-12 months)

BUSINESS LOGIC:
1. BACKFILL FIRST: Replace departing employees immediately (0-3 months) to prevent operational gaps
2. ACTIVE ROLES: Fill immediate open positions (0-1 months) for current business needs
3. GROWTH HIRING: Strategic expansion (3-12 months) for future business growth

TIMELINE BREAKDOWN WITH BUSINESS JUSTIFICATION:

{timeline_text}

Your task: Write one **mathematically-transparent justification per timeframe** explaining:

1. **MATHEMATICAL CALCULATION**: Explain exactly how the count was calculated (X backfill + Y active + Z growth = Total)
2. **BUSINESS PRIORITY**: Why this specific mix of roles is needed in this timeframe
3. **HIRING SEQUENCE**: Backfill FIRST → Active SECOND → Growth THIRD
4. **OPERATIONAL IMPACT**: What happens if this exact count is not met

MANDATORY MATHEMATICAL TRANSPARENCY:
- Start each justification with: "The **[COUNT] hires** needed breaks down as: **[X] backfill + [Y] active + [Z] growth = [TOTAL]**"
- Explain WHY each component is allocated to this timeframe
- Show the mathematical logic: "Backfill cannot wait (departing employees), Active is critical (immediate needs), Growth can be planned"
- Reference the business sequence: "Priority 1: Replace [X] departing employees, Priority 2: Fill [Y] immediate roles, Priority 3: Plan [Z] expansion roles"

CRITICAL HIRING SEQUENCE (NO EXCEPTIONS):
1. **BACKFILL FIRST** (0-3 months): Replace departing employees immediately - operational necessity
2. **ACTIVE SECOND** (0-1 months): Fill immediate open roles - current business needs
3. **GROWTH THIRD** (3-12 months): Strategic expansion - future business planning

Requirements for MATHEMATICAL CLARITY:
- Use EXACT NUMBERS from the breakdown: "12 hires = 8 backfill + 3 active + 1 growth"
- Explain WHY this timeframe: "Backfill in month 1 because employees are leaving now"
- Show business consequences: "Missing 8 backfill = 8 team gaps = operational disruption"
- NO NEGATIVE NUMBERS: All components must be positive or zero
- VERIFY MATH: Always show that components add up to total

Return exactly 5 bullet points (no titles, no intro) in the same order as the timeframes.
""".strip()


def parse_justifications(response: str, expected_count: int) -> list:
    """
    Extracts bullet point justifications from GPT response text.
    Returns a list of strings aligned with the timeline order.
    Pads or trims if mismatch in count.
    """
    lines = response.splitlines()
    bullets = [
        line.lstrip("-•0123456789. ").strip()
        for line in lines
        if line.strip() and not line.lower().startswith("insight")  # skip junk
    ]

    # Pad or trim to match timeline length
    if len(bullets) < expected_count:
        bullets += [""] * (expected_count - len(bullets))
    else:
        bullets = bullets[:expected_count]

    return bullets
