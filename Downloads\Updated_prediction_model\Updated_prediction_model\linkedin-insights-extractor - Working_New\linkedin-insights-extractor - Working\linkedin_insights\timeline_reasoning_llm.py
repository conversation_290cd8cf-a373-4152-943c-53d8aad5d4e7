# timeline_reasoning_llm.py

from linkedin_insights.llm_utils import call_gpt
from linkedin_insights.linkedin_insights_llm_predictor import predict_headcount_llm
import math


def add_reasoning(timeline, role, insights):
    """
    Given a list of {timeframe, count}, returns the same list with mathematically-proven reasoning added per bucket.
    Uses enhanced mathematical analysis to justify each hiring count.
    """
    # Get the full prediction details for mathematical breakdown
    full_prediction = predict_headcount_llm(insights, role=role, return_full=True)

    # Calculate mathematical justifications
    mathematical_breakdown = calculate_mathematical_breakdown(timeline, full_prediction, insights)

    # Build enhanced prompt with mathematical context
    prompt = build_enhanced_prompt(timeline, role, insights, mathematical_breakdown, full_prediction)
    response = call_gpt(prompt, temperature=0.2)  # Lower temperature for more precise reasoning

    # Parse bullet point responses
    justifications = parse_justifications(response, len(timeline))

    # Attach reasoning per bucket
    updated = []
    for i, bucket in enumerate(timeline):
        updated.append({
            "timeframe": bucket["timeframe"],
            "count": bucket["count"],
            "reasoning": justifications[i],
            "mathematical_breakdown": mathematical_breakdown[i] if i < len(mathematical_breakdown) else None,
        })

    return updated


def calculate_mathematical_breakdown(timeline, full_prediction, insights):
    """
    Calculate detailed mathematical breakdown for each timeframe.
    Returns list of mathematical justifications per bucket.
    """
    calc_details = full_prediction.get("calc", {})
    result_details = full_prediction.get("result", {})

    # Extract key metrics
    total_headcount = result_details.get("headcount", 0)
    active_demand = result_details.get("active_demand", 0)
    growth_demand = result_details.get("growth_demand", 0)
    backfill_demand = result_details.get("backfill_demand", 0)

    # Get timeline weights for proportional allocation
    bucket_weights = [1, 2, 3, 3, 3]  # From config.py BUCKETS
    total_weight = sum(bucket_weights)

    # Calculate proportional breakdown per timeframe
    breakdown = []
    for i, bucket in enumerate(timeline):
        weight = bucket_weights[i] if i < len(bucket_weights) else 1
        proportion = weight / total_weight

        # Calculate demand components for this timeframe
        timeframe_active = math.ceil(active_demand * proportion)
        timeframe_growth = math.ceil(growth_demand * proportion)
        timeframe_backfill = math.ceil(backfill_demand * proportion)

        # Get market timing factors
        market_factors = get_market_timing_factors(i, insights)

        # Calculate business impact metrics
        business_metrics = calculate_business_impact(bucket["count"], timeframe_active, timeframe_growth, timeframe_backfill, insights)

        breakdown.append({
            "timeframe": bucket["timeframe"],
            "count": bucket["count"],
            "weight": weight,
            "proportion": round(proportion, 3),
            "active_demand_component": timeframe_active,
            "growth_demand_component": timeframe_growth,
            "backfill_demand_component": timeframe_backfill,
            "market_factors": market_factors,
            "business_impact": business_metrics,
            "mathematical_total": timeframe_active + timeframe_growth + timeframe_backfill,
            "verification": timeframe_active + timeframe_growth + timeframe_backfill == bucket["count"]
        })

    return breakdown


def get_market_timing_factors(timeframe_index, insights):
    """
    Extract market timing factors that justify hiring in specific timeframes.
    """
    factors = {
        "seasonal_multiplier": 1.0,
        "attrition_risk": "standard",
        "demand_urgency": "normal",
        "budget_cycle": "regular"
    }

    # Extract seasonal patterns if available
    hiring_trends = insights.get("hiring_trends", {})
    seasonal_patterns = hiring_trends.get("seasonal_patterns", [])

    if seasonal_patterns:
        # Map timeframe index to quarters (rough approximation)
        quarter_map = {0: "Q1", 1: "Q1", 2: "Q2", 3: "Q3", 4: "Q4"}
        quarter = quarter_map.get(timeframe_index, "Q1")

        for pattern in seasonal_patterns:
            if pattern.get("quarter") == quarter:
                factors["seasonal_multiplier"] = pattern.get("hiring_multiplier", 1.0)
                break

    # Determine urgency based on timeframe
    if timeframe_index == 0:  # 0-1 month
        factors["demand_urgency"] = "critical"
        factors["attrition_risk"] = "immediate"
    elif timeframe_index == 1:  # 1-3 months
        factors["demand_urgency"] = "high"
        factors["attrition_risk"] = "near-term"
    else:
        factors["demand_urgency"] = "planned"
        factors["attrition_risk"] = "projected"

    return factors


def calculate_business_impact(count, active, growth, backfill, insights):
    """
    Calculate business impact metrics for hiring decisions.
    """
    # Extract market intelligence
    competitive_landscape = insights.get("competitive_landscape", {})
    hiring_trends = insights.get("hiring_trends", {})

    # Calculate cost of delay (assuming average time to fill is 120 days)
    avg_time_to_fill = hiring_trends.get("market_demand", {}).get("ai_engineers", {}).get("avg_time_to_fill", 120)
    cost_of_delay_days = avg_time_to_fill * 0.8  # 80% of time to fill as delay cost

    # Calculate competitive risk
    talent_competition = competitive_landscape.get("talent_competition", "Medium")
    competition_multiplier = {"High": 1.5, "Medium": 1.2, "Low": 1.0}.get(talent_competition, 1.2)

    # Revenue impact estimation (rough calculation)
    # Assume each AI engineer contributes ~$500K annually in value
    annual_value_per_engineer = 500000
    monthly_value_per_engineer = annual_value_per_engineer / 12

    return {
        "revenue_at_risk": count * monthly_value_per_engineer,
        "cost_of_delay_days": cost_of_delay_days,
        "competition_risk_multiplier": competition_multiplier,
        "active_roles_urgency": "critical" if active > 0 else "none",
        "growth_impact": "high" if growth > count * 0.4 else "moderate",
        "attrition_risk": "immediate" if backfill > count * 0.3 else "manageable"
    }


def build_enhanced_prompt(timeline, role, insights, mathematical_breakdown, full_prediction):
    """
    Builds an enhanced prompt with mathematical breakdown and business justification.
    """
    # Build timeline description with mathematical breakdown
    timeline_description = []
    for i, bucket in enumerate(timeline):
        breakdown = mathematical_breakdown[i] if i < len(mathematical_breakdown) else {}

        desc = f"- {bucket['timeframe']}: {bucket['count']} hire(s)"
        if breakdown:
            desc += f"\n  • Active demand component: {breakdown.get('active_demand_component', 0)}"
            desc += f"\n  • Growth demand component: {breakdown.get('growth_demand_component', 0)}"
            desc += f"\n  • Backfill demand component: {breakdown.get('backfill_demand_component', 0)}"
            desc += f"\n  • Weight factor: {breakdown.get('weight', 1)} ({breakdown.get('proportion', 0)*100:.1f}% of total)"

            market_factors = breakdown.get('market_factors', {})
            if market_factors.get('seasonal_multiplier', 1.0) != 1.0:
                desc += f"\n  • Seasonal multiplier: {market_factors['seasonal_multiplier']}"
            desc += f"\n  • Demand urgency: {market_factors.get('demand_urgency', 'normal')}"

        timeline_description.append(desc)

    timeline_text = "\n\n".join(timeline_description)

    # Extract key prediction metrics
    result = full_prediction.get("result", {})
    total_active = result.get("active_demand", 0)
    total_growth = result.get("growth_demand", 0)
    total_backfill = result.get("backfill_demand", 0)
    total_headcount = result.get("headcount", 0)
    confidence = result.get("forecast_confidence", 0.5)

    return f"""
You are an expert in strategic workforce planning and mathematical modeling.

A company is hiring for the role of **{role}**. Here is the MATHEMATICALLY CALCULATED hiring breakdown:

TOTAL PREDICTION: {total_headcount} hires over 12 months (Confidence: {confidence:.2f})
- Active demand (immediate open roles): {total_active}
- Growth demand (expansion needs): {total_growth}
- Backfill demand (attrition replacement): {total_backfill}

TIMELINE BREAKDOWN WITH MATHEMATICAL JUSTIFICATION:

{timeline_text}

Your task: Write one **mathematically-proven justification per timeframe** explaining:

1. **WHY this exact count** is needed (reference the mathematical components)
2. **WHEN this hiring must occur** (business timing, market factors, operational needs)
3. **BUSINESS IMPACT** if this count is not met in this timeframe

Requirements:
- Use SPECIFIC NUMBERS from the mathematical breakdown above
- Reference the demand components (active/growth/backfill) that drive each count
- Explain the business consequences of deviating from these numbers
- Connect to market timing factors (seasonal patterns, attrition cycles, budget periods)
- Be precise and quantitative - avoid generic language
- Each justification should be 3-4 sentences with clear mathematical reasoning

Requirements for ENHANCED REASONING:
- Reference SPECIFIC NUMBERS from the mathematical breakdown
- Explain the BUSINESS CONSEQUENCES of missing each target
- Connect to MARKET DYNAMICS (competition, supply shortage, salary inflation)
- Include RISK QUANTIFICATION where possible
- Show MATHEMATICAL PROOF for why this exact count is optimal

Return exactly 5 bullet points (no titles, no intro) in the same order as the timeframes.

MARKET INTELLIGENCE:
- AI Engineer demand growth: 45% year-over-year
- Supply shortage: 35% gap between demand and available talent
- Average time to fill: 120 days
- Salary inflation: 25% annually
- Competition level: HIGH (85% remote work adoption)

Market Context (JSON):
{insights}
""".strip()


def parse_justifications(response: str, expected_count: int) -> list:
    """
    Extracts bullet point justifications from GPT response text.
    Returns a list of strings aligned with the timeline order.
    Pads or trims if mismatch in count.
    """
    lines = response.splitlines()
    bullets = [
        line.lstrip("-•0123456789. ").strip()
        for line in lines
        if line.strip() and not line.lower().startswith("insight")  # skip junk
    ]

    # Pad or trim to match timeline length
    if len(bullets) < expected_count:
        bullets += [""] * (expected_count - len(bullets))
    else:
        bullets = bullets[:expected_count]

    return bullets
