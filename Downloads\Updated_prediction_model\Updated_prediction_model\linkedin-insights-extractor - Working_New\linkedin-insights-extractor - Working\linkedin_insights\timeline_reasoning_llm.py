# timeline_reasoning_llm.py

from linkedin_insights.llm_utils import call_gpt
from linkedin_insights.linkedin_insights_llm_predictor import predict_headcount_llm
import math


def add_reasoning(timeline, role, insights):
    """
    Given a list of {timeframe, count}, returns the same list with mathematically-proven reasoning added per bucket.
    Uses enhanced mathematical analysis to justify each hiring count.
    """
    # Get the full prediction details for mathematical breakdown
    full_prediction = predict_headcount_llm(insights, role=role, return_full=True)

    # Calculate mathematical justifications
    mathematical_breakdown = calculate_mathematical_breakdown(timeline, full_prediction, insights)

    # Build enhanced prompt with mathematical context
    prompt = build_enhanced_prompt(timeline, role, mathematical_breakdown, full_prediction)
    response = call_gpt(prompt, temperature=0.2)  # Lower temperature for more precise reasoning

    # Parse bullet point responses
    justifications = parse_justifications(response, len(timeline))

    # Attach reasoning per bucket
    updated = []
    for i, bucket in enumerate(timeline):
        updated.append({
            "timeframe": bucket["timeframe"],
            "count": bucket["count"],
            "reasoning": justifications[i],
            "mathematical_breakdown": mathematical_breakdown[i] if i < len(mathematical_breakdown) else None,
        })

    return updated


def calculate_mathematical_breakdown(timeline, full_prediction, insights):
    """
    Calculate detailed mathematical breakdown for each timeframe.
    Returns list of mathematical justifications per bucket.
    """
    calc_details = full_prediction.get("calc", {})
    result_details = full_prediction.get("result", {})

    # Extract key metrics
    total_headcount = result_details.get("headcount", 0)
    active_demand = result_details.get("active_demand", 0)
    growth_demand = result_details.get("growth_demand", 0)
    backfill_demand = result_details.get("backfill_demand", 0)

    # Get timeline weights for proportional allocation
    bucket_weights = [1, 2, 3, 3, 3]  # From config.py BUCKETS
    total_weight = sum(bucket_weights)

    # Calculate BUSINESS-LOGICAL breakdown per timeframe
    # BACKFILL (attrition replacement) = URGENT (0-3 months priority)
    # ACTIVE (immediate roles) = CRITICAL (0-1 month priority)
    # GROWTH (expansion) = PLANNED (distributed across 3-12 months)

    breakdown = []

    # Distribute backfill demand (URGENT - prioritize first 3 months)
    backfill_distribution = distribute_backfill_demand(backfill_demand)

    # Distribute active demand (CRITICAL - prioritize first month)
    active_distribution = distribute_active_demand(active_demand)

    # Distribute growth demand (PLANNED - focus on months 3-12)
    growth_distribution = distribute_growth_demand(growth_demand)

    for i, bucket in enumerate(timeline):
        timeframe_active = active_distribution[i]
        timeframe_growth = growth_distribution[i]
        timeframe_backfill = backfill_distribution[i]

        # Get market timing factors
        market_factors = get_market_timing_factors(i, insights)

        # Calculate business impact metrics
        business_metrics = calculate_business_impact(bucket["count"], timeframe_active, timeframe_growth, timeframe_backfill, insights)

        # Calculate business logic priority
        priority_logic = determine_priority_logic(i, timeframe_active, timeframe_growth, timeframe_backfill)

        breakdown.append({
            "timeframe": bucket["timeframe"],
            "count": bucket["count"],
            "active_demand_component": timeframe_active,
            "growth_demand_component": timeframe_growth,
            "backfill_demand_component": timeframe_backfill,
            "market_factors": market_factors,
            "business_impact": business_metrics,
            "priority_logic": priority_logic,
            "mathematical_total": timeframe_active + timeframe_growth + timeframe_backfill,
            "verification": timeframe_active + timeframe_growth + timeframe_backfill == bucket["count"],
            "business_justification": get_business_justification(i, timeframe_active, timeframe_growth, timeframe_backfill)
        })

    return breakdown


def distribute_backfill_demand(total_backfill):
    """
    Distribute backfill demand with URGENT priority (0-3 months).
    Backfill = replacing people who left due to attrition - CANNOT WAIT!
    """
    if total_backfill == 0:
        return [0, 0, 0, 0, 0]

    # 70% in first 3 months (0-1: 30%, 1-3: 40%), 30% in remaining months
    distribution = [
        math.ceil(total_backfill * 0.30),  # 0-1 month: 30% (IMMEDIATE)
        math.ceil(total_backfill * 0.40),  # 1-3 months: 40% (URGENT)
        math.ceil(total_backfill * 0.15),  # 3-6 months: 15% (PLANNED)
        math.ceil(total_backfill * 0.10),  # 6-9 months: 10% (PLANNED)
        math.ceil(total_backfill * 0.05),  # 9+ months: 5% (PLANNED)
    ]

    # Adjust to match total exactly
    current_sum = sum(distribution)
    diff = total_backfill - current_sum
    if diff != 0:
        # Add/subtract from the largest bucket (1-3 months)
        distribution[1] += diff

    return distribution


def distribute_active_demand(total_active):
    """
    Distribute active demand with CRITICAL priority (0-1 months).
    Active = immediate open roles that need to be filled NOW.
    """
    if total_active == 0:
        return [0, 0, 0, 0, 0]

    # 80% in first month, 20% in second timeframe
    distribution = [
        math.ceil(total_active * 0.80),  # 0-1 month: 80% (CRITICAL)
        math.ceil(total_active * 0.20),  # 1-3 months: 20% (HIGH)
        0,  # 3-6 months: 0% (active roles can't wait this long)
        0,  # 6-9 months: 0%
        0,  # 9+ months: 0%
    ]

    # Adjust to match total exactly
    current_sum = sum(distribution)
    diff = total_active - current_sum
    if diff != 0:
        distribution[0] += diff  # Add to first month

    return distribution


def distribute_growth_demand(total_growth):
    """
    Distribute growth demand with PLANNED priority (3-12 months).
    Growth = expansion roles for future business needs.
    """
    if total_growth == 0:
        return [0, 0, 0, 0, 0]

    # Minimal in first 3 months, majority in months 3-12
    distribution = [
        math.ceil(total_growth * 0.05),  # 0-1 month: 5% (minimal)
        math.ceil(total_growth * 0.10),  # 1-3 months: 10% (some planning)
        math.ceil(total_growth * 0.30),  # 3-6 months: 30% (MAIN GROWTH)
        math.ceil(total_growth * 0.30),  # 6-9 months: 30% (MAIN GROWTH)
        math.ceil(total_growth * 0.25),  # 9+ months: 25% (STRATEGIC)
    ]

    # Adjust to match total exactly
    current_sum = sum(distribution)
    diff = total_growth - current_sum
    if diff != 0:
        # Add/subtract from the largest bucket (3-6 months)
        distribution[2] += diff

    return distribution


def determine_priority_logic(timeframe_index, active, growth, backfill):
    """
    Determine the business priority logic for each timeframe.
    """
    timeframes = ["0–1 month", "1–3 months", "3–6 months", "6–9 months", "9+ months"]
    timeframe_name = timeframes[timeframe_index] if timeframe_index < len(timeframes) else "unknown"

    if timeframe_index == 0:  # 0-1 month
        return {
            "primary_focus": "CRITICAL: Active roles + Immediate backfill",
            "urgency": "MAXIMUM",
            "rationale": f"Active roles ({active}) cannot wait - immediate business impact. Backfill ({backfill}) prevents operational gaps.",
            "delay_risk": "HIGH - Direct revenue impact and team overload"
        }
    elif timeframe_index == 1:  # 1-3 months
        return {
            "primary_focus": "URGENT: Remaining backfill + Some growth",
            "urgency": "HIGH",
            "rationale": f"Complete backfill hiring ({backfill}) to stabilize teams. Begin growth hiring ({growth}) for Q2 needs.",
            "delay_risk": "MEDIUM-HIGH - Attrition compounds, growth delayed"
        }
    else:  # 3+ months
        return {
            "primary_focus": "PLANNED: Growth-focused expansion",
            "urgency": "MODERATE",
            "rationale": f"Strategic growth hiring ({growth}) for future capacity. Minimal backfill ({backfill}) as teams stabilized.",
            "delay_risk": "MEDIUM - Strategic positioning and market opportunities"
        }


def get_business_justification(timeframe_index, active, growth, backfill):
    """
    Generate specific business justification based on demand components.
    """
    total = active + growth + backfill

    if timeframe_index == 0:  # 0-1 month
        return f"CRITICAL HIRING: {active} immediate roles + {backfill} attrition replacements = {total} hires needed to prevent operational disruption"
    elif timeframe_index == 1:  # 1-3 months
        return f"URGENT STABILIZATION: {backfill} remaining backfills + {growth} early growth roles = {total} hires to complete team stabilization"
    else:  # 3+ months
        return f"STRATEGIC GROWTH: {growth} expansion roles + {backfill} projected backfills = {total} hires for planned business growth"


def get_market_timing_factors(timeframe_index, insights):
    """
    Extract market timing factors that justify hiring in specific timeframes.
    """
    factors = {
        "seasonal_multiplier": 1.0,
        "attrition_risk": "standard",
        "demand_urgency": "normal",
        "budget_cycle": "regular"
    }

    # Extract seasonal patterns if available
    hiring_trends = insights.get("hiring_trends", {})
    seasonal_patterns = hiring_trends.get("seasonal_patterns", [])

    if seasonal_patterns:
        # Map timeframe index to quarters (rough approximation)
        quarter_map = {0: "Q1", 1: "Q1", 2: "Q2", 3: "Q3", 4: "Q4"}
        quarter = quarter_map.get(timeframe_index, "Q1")

        for pattern in seasonal_patterns:
            if pattern.get("quarter") == quarter:
                factors["seasonal_multiplier"] = pattern.get("hiring_multiplier", 1.0)
                break

    # Determine urgency based on timeframe
    if timeframe_index == 0:  # 0-1 month
        factors["demand_urgency"] = "critical"
        factors["attrition_risk"] = "immediate"
    elif timeframe_index == 1:  # 1-3 months
        factors["demand_urgency"] = "high"
        factors["attrition_risk"] = "near-term"
    else:
        factors["demand_urgency"] = "planned"
        factors["attrition_risk"] = "projected"

    return factors


def calculate_business_impact(count, active, growth, backfill, insights):
    """
    Calculate business impact metrics for hiring decisions.
    """
    # Extract market intelligence
    competitive_landscape = insights.get("competitive_landscape", {})
    hiring_trends = insights.get("hiring_trends", {})

    # Calculate cost of delay (assuming average time to fill is 120 days)
    avg_time_to_fill = hiring_trends.get("market_demand", {}).get("ai_engineers", {}).get("avg_time_to_fill", 120)
    cost_of_delay_days = avg_time_to_fill * 0.8  # 80% of time to fill as delay cost

    # Calculate competitive risk
    talent_competition = competitive_landscape.get("talent_competition", "Medium")
    competition_multiplier = {"High": 1.5, "Medium": 1.2, "Low": 1.0}.get(talent_competition, 1.2)

    # Revenue impact estimation (rough calculation)
    # Assume each AI engineer contributes ~$500K annually in value
    annual_value_per_engineer = 500000
    monthly_value_per_engineer = annual_value_per_engineer / 12

    return {
        "revenue_at_risk": count * monthly_value_per_engineer,
        "cost_of_delay_days": cost_of_delay_days,
        "competition_risk_multiplier": competition_multiplier,
        "active_roles_urgency": "critical" if active > 0 else "none",
        "growth_impact": "high" if growth > count * 0.4 else "moderate",
        "attrition_risk": "immediate" if backfill > count * 0.3 else "manageable"
    }


def build_enhanced_prompt(timeline, role, mathematical_breakdown, full_prediction):
    """
    Builds an enhanced prompt with mathematical breakdown and business justification.
    """
    # Build timeline description with mathematical breakdown
    timeline_description = []
    for i, bucket in enumerate(timeline):
        breakdown = mathematical_breakdown[i] if i < len(mathematical_breakdown) else {}

        desc = f"- {bucket['timeframe']}: {bucket['count']} hire(s)"
        if breakdown:
            desc += f"\n  • Active demand component: {breakdown.get('active_demand_component', 0)}"
            desc += f"\n  • Growth demand component: {breakdown.get('growth_demand_component', 0)}"
            desc += f"\n  • Backfill demand component: {breakdown.get('backfill_demand_component', 0)}"
            desc += f"\n  • Weight factor: {breakdown.get('weight', 1)} ({breakdown.get('proportion', 0)*100:.1f}% of total)"

            market_factors = breakdown.get('market_factors', {})
            if market_factors.get('seasonal_multiplier', 1.0) != 1.0:
                desc += f"\n  • Seasonal multiplier: {market_factors['seasonal_multiplier']}"
            desc += f"\n  • Demand urgency: {market_factors.get('demand_urgency', 'normal')}"

        timeline_description.append(desc)

    timeline_text = "\n\n".join(timeline_description)

    # Extract key prediction metrics
    result = full_prediction.get("result", {})
    total_active = result.get("active_demand", 0)
    total_growth = result.get("growth_demand", 0)
    total_backfill = result.get("backfill_demand", 0)
    total_headcount = result.get("headcount", 0)
    confidence = result.get("forecast_confidence", 0.5)

    return f"""
You are an expert in strategic workforce planning and mathematical modeling.

A company is hiring for the role of **{role}**. Here is the BUSINESS-LOGICAL hiring breakdown:

TOTAL PREDICTION: {total_headcount} hires over 12 months (Confidence: {confidence:.2f})
- Active demand (immediate open roles): {total_active} → CRITICAL (0-1 months)
- Backfill demand (attrition replacement): {total_backfill} → URGENT (0-3 months)
- Growth demand (expansion needs): {total_growth} → PLANNED (3-12 months)

BUSINESS LOGIC:
1. BACKFILL FIRST: Replace departing employees immediately (0-3 months) to prevent operational gaps
2. ACTIVE ROLES: Fill immediate open positions (0-1 months) for current business needs
3. GROWTH HIRING: Strategic expansion (3-12 months) for future business growth

TIMELINE BREAKDOWN WITH BUSINESS JUSTIFICATION:

{timeline_text}

Your task: Write one **business-logical justification per timeframe** explaining:

1. **WHY this exact count** is needed (reference active/backfill/growth components)
2. **BUSINESS PRIORITY** (backfill urgency vs growth planning)
3. **OPERATIONAL IMPACT** if this count is not met in this timeframe
4. **MATHEMATICAL PROOF** showing the breakdown

CRITICAL BUSINESS LOGIC TO EMPHASIZE:
- BACKFILL (attrition replacement) = URGENT (must hire in 0-3 months to prevent operational gaps)
- ACTIVE (immediate roles) = CRITICAL (must hire in 0-1 months for current business needs)
- GROWTH (expansion) = PLANNED (can be scheduled 3-12 months for strategic growth)

Requirements for BUSINESS-FOCUSED REASONING:
- Emphasize BACKFILL URGENCY in early timeframes (0-3 months) - "Cannot wait, operational necessity"
- Show GROWTH PLANNING in later timeframes (3-12 months) - "Strategic expansion, can be planned"
- Reference SPECIFIC NUMBERS: "X active roles (immediate), Y backfill roles (urgent), Z growth roles (planned)"
- Explain WHY backfill cannot wait: "Departing employees create immediate gaps"
- Explain WHY growth can wait: "Expansion roles support future business, not current operations"
- Quantify BUSINESS RISKS: operational disruption, team overload, missed deadlines vs strategic opportunities

Return exactly 5 bullet points (no titles, no intro) in the same order as the timeframes.
""".strip()


def parse_justifications(response: str, expected_count: int) -> list:
    """
    Extracts bullet point justifications from GPT response text.
    Returns a list of strings aligned with the timeline order.
    Pads or trims if mismatch in count.
    """
    lines = response.splitlines()
    bullets = [
        line.lstrip("-•0123456789. ").strip()
        for line in lines
        if line.strip() and not line.lower().startswith("insight")  # skip junk
    ]

    # Pad or trim to match timeline length
    if len(bullets) < expected_count:
        bullets += [""] * (expected_count - len(bullets))
    else:
        bullets = bullets[:expected_count]

    return bullets
