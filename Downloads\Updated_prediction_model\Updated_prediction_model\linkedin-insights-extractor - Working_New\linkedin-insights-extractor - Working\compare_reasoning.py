#!/usr/bin/env python3
"""
Comparison script to show the improvement in reasoning quality.
"""

import json
from linkedin_insights.linkedin_insights_llm_predictor import predict_headcount_llm
from linkedin_insights.timeline_allocator import allocate_buckets
from linkedin_insights.timeline_reasoning_llm import add_reasoning

def compare_reasoning_quality():
    """Compare the enhanced reasoning with mathematical breakdown."""
    
    # Load sample insights
    with open("sample_insights.json", "r") as f:
        insights = json.load(f)
    
    role = "AI Engineer"
    
    print("=" * 80)
    print("ENHANCED REASONING SYSTEM - MATHEMATICAL ANALYSIS")
    print("=" * 80)
    
    # Step 1: Get detailed prediction
    full_result = predict_headcount_llm(insights, role=role, return_full=True)
    
    # Extract calculation details
    calc = full_result["calc"]
    result = full_result["result"]
    
    print(f"\n📊 TOTAL PREDICTION ANALYSIS:")
    print(f"   Total headcount needed: {result['headcount']} AI Engineers")
    print(f"   Forecast confidence: {result['forecast_confidence']:.2f}")
    print(f"\n🔢 MATHEMATICAL BREAKDOWN:")
    print(f"   Active demand (immediate roles): {result['active_demand']}")
    print(f"   Growth demand (expansion): {result['growth_demand']}")
    print(f"   Backfill demand (attrition): {result['backfill_demand']}")
    print(f"   Formula verification: {result['active_demand']} + {result['growth_demand']} + {result['backfill_demand']} = {result['headcount']}")
    
    # Step 2: Show timeline allocation
    timeline = allocate_buckets(result['headcount'], role, insights)
    
    print(f"\n📅 TIMELINE ALLOCATION:")
    total_check = 0
    for bucket in timeline:
        print(f"   {bucket['timeframe']}: {bucket['count']} hires")
        total_check += bucket['count']
    print(f"   Verification: Total = {total_check} (matches prediction: {total_check == result['headcount']})")
    
    # Step 3: Enhanced reasoning with mathematical justification
    timeline_with_reasoning = add_reasoning(timeline, role, insights)
    
    print(f"\n🧮 ENHANCED REASONING WITH MATHEMATICAL PROOF:")
    print("=" * 80)
    
    for i, bucket in enumerate(timeline_with_reasoning):
        print(f"\n📋 TIMEFRAME: {bucket['timeframe']}")
        print(f"   Target: {bucket['count']} hires")
        
        # Show mathematical breakdown if available
        if 'mathematical_breakdown' in bucket and bucket['mathematical_breakdown']:
            breakdown = bucket['mathematical_breakdown']
            print(f"   Mathematical Components:")
            print(f"   • Active demand: {breakdown.get('active_demand_component', 0)}")
            print(f"   • Growth demand: {breakdown.get('growth_demand_component', 0)}")
            print(f"   • Backfill demand: {breakdown.get('backfill_demand_component', 0)}")
            print(f"   • Weight factor: {breakdown.get('weight', 1)} ({breakdown.get('proportion', 0)*100:.1f}% of total)")
            
            market_factors = breakdown.get('market_factors', {})
            print(f"   Market Timing:")
            print(f"   • Seasonal multiplier: {market_factors.get('seasonal_multiplier', 1.0)}")
            print(f"   • Demand urgency: {market_factors.get('demand_urgency', 'normal')}")
            print(f"   • Attrition risk: {market_factors.get('attrition_risk', 'standard')}")
        
        print(f"\n   Business Justification:")
        print(f"   {bucket['reasoning']}")
        print("-" * 60)
    
    # Step 4: Show key improvements
    print(f"\n🎯 KEY IMPROVEMENTS IN REASONING:")
    print("=" * 80)
    print("✅ Mathematical Precision:")
    print("   - Exact breakdown of active/growth/backfill demand per timeframe")
    print("   - Proportional allocation based on weighted timeline buckets")
    print("   - Verification that components sum to total prediction")
    
    print("\n✅ Business Impact Analysis:")
    print("   - Specific consequences of missing hiring targets")
    print("   - Connection to operational needs and project timelines")
    print("   - Risk assessment for each timeframe")
    
    print("\n✅ Market Timing Intelligence:")
    print("   - Seasonal hiring patterns and multipliers")
    print("   - Demand urgency classification (critical/high/planned)")
    print("   - Attrition risk timing (immediate/near-term/projected)")
    
    print("\n✅ Quantitative Justification:")
    print("   - Every hiring count backed by mathematical formula")
    print("   - Clear explanation of WHY each number is needed")
    print("   - Traceable from total prediction to timeframe allocation")
    
    return timeline_with_reasoning

if __name__ == "__main__":
    compare_reasoning_quality()
