#!/usr/bin/env python3
"""
Debug script to understand why we're getting small numbers and negative adjustments.
"""

import json
from linkedin_insights.linkedin_insights_llm_predictor import predict_headcount_llm
from linkedin_insights.timeline_allocator import allocate_buckets
from linkedin_insights.timeline_reasoning_llm import add_reasoning

def debug_small_numbers():
    """Debug the small numbers issue."""
    
    # Create a smaller test case to understand the issue
    small_insights = {
        "company_profile": {
            "employees_current": 1000,
            "hires_last_12_months": 150,
            "growth_rate_percent_1y": 10,
            "attrition_percent_1y": 8,
            "open_jobs": 25,
            "industry": "Technology",
            "headquarters": "Seattle, WA",
            "founded_year": 1975,
            "company_type": "Public Company"
        },
        "top_functions_percent": [
            {"function": "Engineering", "percent_employees": 40}
        ],
        "function_headcount": {
            "Engineering": 400
        },
        "skills": {
            "top_skills": [
                {"skill": "Python", "professionals": 80},
                {"skill": "Machine Learning", "professionals": 50}
            ]
        },
        "attrition_breakdown": {
            "by_function": [
                {"function": "Engineering", "attrition_percent": 10}
            ]
        }
    }
    
    role = "AI Engineer"
    
    print("=" * 80)
    print("DEBUGGING SMALL NUMBERS ISSUE")
    print("=" * 80)
    
    # Step 1: Get prediction
    full_result = predict_headcount_llm(small_insights, role=role, return_full=True)
    result = full_result["result"]
    
    print(f"\n📊 PREDICTION BREAKDOWN:")
    print(f"   Active demand: {result['active_demand']}")
    print(f"   Backfill demand: {result['backfill_demand']}")
    print(f"   Growth demand: {result['growth_demand']}")
    print(f"   Total headcount: {result['headcount']}")
    
    # Step 2: Show timeline allocation
    timeline = allocate_buckets(result['headcount'], role, small_insights)
    
    print(f"\n📅 TIMELINE ALLOCATION:")
    for bucket in timeline:
        print(f"   {bucket['timeframe']}: {bucket['count']} hires")
    
    # Step 3: Show detailed breakdown
    timeline_with_reasoning = add_reasoning(timeline, role, small_insights)
    
    print(f"\n🔍 DETAILED BREAKDOWN:")
    for bucket in timeline_with_reasoning:
        breakdown = bucket.get('mathematical_breakdown', {})
        if breakdown:
            active = breakdown.get('active_demand_component', 0)
            backfill = breakdown.get('backfill_demand_component', 0)
            growth = breakdown.get('growth_demand_component', 0)
            
            print(f"\n{bucket['timeframe']}: {bucket['count']} total")
            print(f"   Active: {active}")
            print(f"   Backfill: {backfill}")
            print(f"   Growth: {growth}")
            print(f"   Sum: {active + backfill + growth}")
            print(f"   Matches: {active + backfill + growth == bucket['count']}")
    
    # Step 4: Test distribution functions directly
    print(f"\n🧮 TESTING DISTRIBUTION FUNCTIONS:")
    
    from linkedin_insights.timeline_reasoning_llm import distribute_active_demand, distribute_backfill_demand, distribute_growth_demand
    
    print(f"\nActive distribution ({result['active_demand']} total):")
    active_dist = distribute_active_demand(result['active_demand'])
    print(f"   {active_dist} = {sum(active_dist)}")
    
    print(f"\nBackfill distribution ({result['backfill_demand']} total):")
    backfill_dist = distribute_backfill_demand(result['backfill_demand'])
    print(f"   {backfill_dist} = {sum(backfill_dist)}")
    
    print(f"\nGrowth distribution ({result['growth_demand']} total):")
    growth_dist = distribute_growth_demand(result['growth_demand'])
    print(f"   {growth_dist} = {sum(growth_dist)}")
    
    # Step 5: Show the reasoning
    print(f"\n📝 REASONING OUTPUT:")
    for bucket in timeline_with_reasoning:
        print(f"\n{bucket['timeframe']}: {bucket['count']} hires")
        print(f"Reasoning: {bucket['reasoning']}")

if __name__ == "__main__":
    debug_small_numbers()
